import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import server from '@/app/api/server';

// Types
export interface Desk {
  id: string;
  name: string;
  description?: string;
  type: 'standard' | 'standing' | 'adjustable' | 'corner' | 'collaborative';
  location: {
    building?: string;
    floor: string;
    room: string;
    address?: string;
  };
  amenities?: string[];
  equipment?: string[];
  isActive: boolean;
  bookingRules: {
    minBookingDuration: number;
    maxBookingDuration: number;
    advanceBookingDays: number;
    minNoticeHours: number;
  };
  availability: {
    businessHours: {
      start: string;
      end: string;
    };
    workingDays: number[];
    timezone: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface DeskFilters {
  page?: number;
  limit?: number;
  search?: string;
  type?: string;
  isActive?: boolean;
  building?: string;
  floor?: string;
}

export interface CreateDeskInput {
  name: string;
  description?: string;
  type: 'standard' | 'standing' | 'adjustable' | 'corner' | 'collaborative';
  location: {
    building?: string;
    floor: string;
    room: string;
    address?: string;
  };
  amenities?: string[];
  equipment?: string[];
  isActive: boolean;
  bookingRules: {
    minBookingDuration: number;
    maxBookingDuration: number;
    advanceBookingDays: number;
    minNoticeHours: number;
  };
  availability: {
    businessHours: {
      start: string;
      end: string;
    };
    workingDays: number[];
    timezone: string;
  };
}

export interface UpdateDeskInput extends Partial<CreateDeskInput> {
  id: string;
}

export interface DesksResponse {
  data: Desk[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Hooks
export function useDesks(filters: DeskFilters = {}) {
  return useQuery({
    queryKey: ['desks', filters],
    queryFn: async (): Promise<DesksResponse> => {
      const params = new URLSearchParams();
      
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());
      if (filters.search) params.append('search', filters.search);
      if (filters.type) params.append('type', filters.type);
      if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
      if (filters.building) params.append('building', filters.building);
      if (filters.floor) params.append('floor', filters.floor);

      const response = await fetch(`${server.baseURL}/api/desks?${params.toString()}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch desks');
      }

      return response.json();
    },
  });
}

export function useDesk(id: string) {
  return useQuery({
    queryKey: ['desk', id],
    queryFn: async (): Promise<Desk> => {
      const response = await fetch(`${server.baseURL}/api/desks/${id}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch desk');
      }

      return response.json();
    },
    enabled: !!id,
  });
}

export function useCreateDesk() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateDeskInput): Promise<Desk> => {
      const response = await fetch(`${server.baseURL}/api/desks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create desk');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['desks'] });
    },
  });
}

export function useUpdateDesk() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateDeskInput): Promise<Desk> => {
      const { id, ...updateData } = data;
      const response = await fetch(`${server.baseURL}/api/desks/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update desk');
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['desks'] });
      queryClient.invalidateQueries({ queryKey: ['desk', data.id] });
    },
  });
}

export function useDeleteDesk() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`${server.baseURL}/api/desks/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete desk');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['desks'] });
    },
  });
}

// Utility functions
export function getDeskTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    standard: 'Escritorio Estándar',
    standing: 'Escritorio de Pie',
    adjustable: 'Escritorio Ajustable',
    corner: 'Escritorio Esquinero',
    collaborative: 'Espacio Colaborativo',
  };
  return labels[type] || type;
}

export function getDeskStatusColor(isActive: boolean): string {
  return isActive ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700';
}

export function formatDeskLocation(location: Desk['location']): string {
  const parts = [];
  if (location.building) parts.push(location.building);
  parts.push(`Piso ${location.floor}`);
  parts.push(location.room);
  return parts.join(', ');
}
